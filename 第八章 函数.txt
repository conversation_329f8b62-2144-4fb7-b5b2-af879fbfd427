幻灯片 1:
第八章 函数
Python程序设计
教师：冯子晋


幻灯片 2:
第 8 章 函数（上）
8.1 定义函数
8.2 传递实参
8.3 返回值
8.4 传递列表


幻灯片 3:
定义：一段可重复调用的代码块，用于执行特定的任务。
8.1 定义函数

幻灯片 4:

8.1 定义函数

幻灯片 5:

8.1 定义函数

幻灯片 6:

8.1 定义函数

幻灯片 7:

8.1 定义函数

幻灯片 8:

8.1 定义函数

幻灯片 9:

8.1 定义函数
运行结果
Hello!

幻灯片 10:

8.1 定义函数

幻灯片 11:
8.1.2 实参和形参
def greet_user(username):
    """显示简单的问候语"""
    print(f"Hello, {username.title()}!")

greet_user('jesse')
运行结果
Hello, Jesse!

幻灯片 12:
8.1.2 实参和形参

幻灯片 13:
8.1.2 实参和形参

幻灯片 14:
8.1.2 实参和形参
形参（parameter）：username，函数用到的信息的名字。
实参（argument） ：'jesse' ，函数实际使用的信息。

幻灯片 15:
8.2 传递实参
函数定义中可能包含多个形参
传参有位置实参与关键字实参两种

幻灯片 16:
位置实参：形参顺序与实参顺序一致


8.2.1 位置实参
def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet('cat', 'harry')
运行结果
I have a cat.
My cat's name is Harry.

幻灯片 17:
位置实参：形参顺序与实参顺序一致


8.2.1 位置实参
def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet('cat', 'harry')
运行结果
I have a cat.
My cat's name is Harry.

幻灯片 18:
位置实参：形参顺序与实参顺序一致


8.2.1 位置实参
def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet('cat', 'harry')
运行结果
I have a cat.
My cat's name is Harry.

幻灯片 19:
8.2.1 位置实参
如果我们混淆了位置实参的顺序，那就可能会得到一个怪物：


def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet('harry', 'cat')
运行结果
I have a harry.
My harry's name is Cat.


幻灯片 20:
8.2.1 位置实参
如果我们混淆了位置实参的顺序，那就可能会得到一个怪物：


def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet('harry', 'cat')
运行结果
I have a harry.
My harry's name is Cat.


幻灯片 21:
8.2.2 关键字实参
关键字实参：函数调用时，明确指出实参对应的形参。

def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet(animal_type='cat', pet_name='harry')
describe_pet(pet_name='harry', animal_type='cat')






幻灯片 22:
8.2.2 关键字实参
关键字实参：函数调用时，明确指出实参对应的形参。

def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet(animal_type='cat', pet_name='harry')
describe_pet(pet_name='harry', animal_type='cat')






幻灯片 23:
8.2.3 默认值
形参可以指定默认值
def describe_pet(pet_name, animal_type='dog'):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet('willie')     #没有第二个参数，第二个参数用默认值

运行结果
I have a dog.
My dog's name is Willie.




幻灯片 24:
8.2.3 默认值
形参可以指定默认值，同时使用关键字实参
def describe_pet(pet_name, animal_type='dog'):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet(pet_name='willie') #没有指定animal_type,用默认值
运行结果
I have a dog.
My dog's name is Willie.




幻灯片 25:
8.2.3 默认值
有实参则默认值被忽略
运行结果
I have a cat.
My cat's name is Harry.

幻灯片 26:
哪些函数调用的方式是等价的？
8.2.4 等效的函数调用
def describe_pet(pet_name, animal_type='dog'):
    ...
describe_pet('willie')
describe_pet(pet_name='willie')
describe_pet('harry', 'cat')
describe_pet(pet_name='harry', animal_type='cat')
describe_pet(animal_type='cat', pet_name='harry')

幻灯片 27:
哪些函数调用的方式是等价的？
8.2.4 等效的函数调用
def describe_pet(pet_name, animal_type='dog'):
    ...

幻灯片 28:
哪些函数调用的方式是等价的？
8.2.4 等效的函数调用
def describe_pet(pet_name, animal_type='dog'):
    ...

幻灯片 29:




8.2.4 随堂练习1：等效的函数调用
定义一个函数 introduce(name, hobby="编程")，函数功能为打印出 "我是XXX，我喜欢YYY"。
要求：
自己编写完整的函数体
用以下三种方式调用该函数：

（1）只传入一个参数

（2）使用位置传参传入两个参数

（3）使用关键字传参传入两个参数

幻灯片 30:
8.2.4 随堂练习1：等效的函数调用
def introduce(name, hobby="编程"): # 第一步：定义函数
    print(f"我是{name}，我喜欢{hobby}")
introduce("小明") # （1）只传入一个参数
introduce("小红", "打篮球") # （2）位置传参两个参数
introduce(hobby="读书", name="小刚") # （3）关键字传参两个参数


幻灯片 31:
8.2.5 避免实参错误
实参数量与所需的形参数量不一致



def describe_pet(pet_name, animal_type='dog'): #需要一个或两个实参
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet() #有零个实参
运行结果
Traceback (most recent call last):
   File "pets.py", line 6, in <module>
     describe_pet()
     ^^^^^^^^^^^^^^
TypeError: describe_pet() missing 2 required positional arguments:
    'animal_type' and 'pet_name'
1
2
3

幻灯片 32:
关键词实参必须在位置实参之后（申明及调用时）：




def describe_pet(pet_name, animal_type='dog'):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")
describe_pet(animal_type='cat', 'harry')
运行结果
Traceback (most recent call last):
   File "pets.py", line 6, in <module>
     describe_pet(animal_type='cat', 'harry’)
     ^
SyntaxError: positional argument follows keyword argument
8.2.5 避免实参错误

幻灯片 33:




8.2.6 参数：讨论与小结
1.任何时候都应该使用关键词传参吗？

2.传参的最佳实践是什么？

幻灯片 34:
8.3 返回值
函数中使用返回语句来返回的一个或一组值


幻灯片 35:


8.3.1 返回简单的值
def get_formatted_name(first_name, last_name):
    """返回标准格式的姓名"""
    full_name = f"{first_name} {last_name}"
    return full_name.title()

musician = get_formatted_name('jimi', 'hendrix')
print(musician)


返回关键字



函数返回的值


幻灯片 36:


8.3.1 返回简单的值
def get_formatted_name(first_name, last_name):
    """返回标准格式的姓名"""
    full_name = f"{first_name} {last_name}"
    return full_name.title()

musician = get_formatted_name('jimi', 'hendrix')
print(musician)







Jimi Hendrix

运行结果
Jimi Hendrix

幻灯片 37:

def get_formatted_name(first_name, middle_name, last_name):
    """返回标准格式的姓名：名 中间名 姓"""
    full_name = f"{first_name} {middle_name} {last_name}"
    return full_name.title()

musician = get_formatted_name('john', 'lee', 'hooker')
print(musician)
运行结果
John Lee Hooker
8.3.2 如何让实参可选

幻灯片 38:

def get_formatted_name(first_name, last_name, middle_name=''):
    """返回标准格式的姓名"""
    if middle_name:
        full_name = f"{first_name} {middle_name} {last_name}"
    else:
        full_name = f"{first_name} {last_name}"
    return full_name.title()

musician = get_formatted_name(last_name='jimi’, first_name='hendrix')
print(musician)
musician = get_formatted_name(first_name='john’, last_name='hooker’, middle_name='lee')
print(musician)

8.3.2 如何让实参可选
运行结果
Jimi Hendrix
John Lee Hooker




幻灯片 39:
函数可返回任何类型的值，包括列表和字典等较为复杂的数据结构：
def build_person(first_name, last_name):
    """返回一个字典，其中包含有关一个人的信息"""
    # 在这里写一行代码，实现运行结果
    return person
musician = build_person('jimi', 'hendrix')
print(musician)
运行结果
{'first': 'jimi', 'last': 'hendrix'}
8.3.3 返回字典：随堂练习

幻灯片 40:
函数可返回任何类型的值，包括列表和字典等较为复杂的数据结构：
运行结果
{'first': 'jimi', 'last': 'hendrix'}
8.3.3 返回字典
def build_person(first_name, last_name):
    """返回一个字典，其中包含有关一个人的信息"""
    # 在这里写一行代码，实现运行结果
    return person
musician = build_person('jimi', 'hendrix')
print(musician)

幻灯片 41:
增加一个年龄可选值，其默认值为 None ，表示没有值或是占位值：
def build_person(first_name, last_name, age=None):
    """返回一个字典，其中包含有关一个人的信息"""
    person = {'first': first_name, 'last': last_name}
    if age:
        person['age'] = age
    return person

musician = build_person('jimi', 'hendrix', age=27)
print(musician)
运行结果
{'first': 'jimi', 'last': 'hendrix', 'age': 27}
8.3.3 返回字典

幻灯片 42:

def get_formatted_name(first_name, last_name):
    """返回规范格式的姓名"""
    full_name = f"{first_name} {last_name}"
    return full_name.title()

while True:
    print("\nPlease tell me your name:")
    f_name = input("First name: ")
    l_name = input("Last name: ")
    formatted_name = get_formatted_name(f_name, l_name)
    print(f"\nHello, {formatted_name}!")
小心无限循环！
8.3.4 结合函数和 while 循环

幻灯片 43:
小练习，如何让用户？
def get_formatted_name(first_name, last_name):
    ...

while True:
    print("\nPlease tell me your name:") 
    print("(enter 'q' at any time to quit)")
    f_name = input("First name: ")
    if f_name == 'q':
        break
    l_name = input("Last name: ") 
    if f_name == 'q':
        break
    formatted_name = get_formatted_name(f_name, l_name)
    print(f"\nHello, {formatted_name}!")
8.3.4 结合使用函数和 while 循环
可以随时输入 'q' 退出程序

幻灯片 44:

运行结果
Please tell me your name:
(enter 'q' at any time to quit)
First name:
Last name:

Hello, Eric Matthes!

Please tell me your name:
(enter 'q' at any time to quit)
First name: 
q
8.3.4 结合使用函数和 while 循环

幻灯片 45:

8.3.5 结合使用函数和 while 循环：随堂讨论
def make_milk_tea(flavor, sweetness):
    """返回格式化的奶茶订单描述"""
    order =f"{sweetness}糖的{flavor}奶茶"
    return order

while True:
    print("\n欢迎来到ChatGPT奶茶店！")
    flavor =input("你想要什么口味的奶茶？（例如：珍珠、芋泥、椰香）")
    sweetness =input("你想要的甜度是多少？（例如：全糖、半糖、无糖）")
    tea_order = make_milk_tea(flavor, sweetness)
    print(f"\n你的订单是：{tea_order}，请稍等片刻哦~")
    again =input("还要帮朋友点一杯吗？（yes/no）")
    if again.lower() !='yes':
        print("谢谢光临，祝你天天开心！")
        break

幻灯片 46:

8.3.5 结合使用函数和 while 循环：随堂讨论
补全代码，完成点奶茶小程序
def make_milk_tea(flavor, sweetness):
    """返回格式化的奶茶订单描述"""
    order =f"{sweetness}糖的{flavor}奶茶"
    # 补全代码
while True:
    print("\n欢迎来到ChatGPT奶茶店！")
    flavor =input("你想要什么口味的奶茶？（例如：珍珠、芋泥、椰香）")
    sweetness =input("你想要的甜度是多少？（例如：全糖、半糖、无糖）")
    tea_order = make_milk_tea(flavor, sweetness)
    print(f"\n你的订单是：{tea_order}，请稍等片刻哦~")
    again =input("还要帮朋友点一杯吗？（yes/no）"
    # 补全代码，让点奶茶可以停止

幻灯片 47:
假设有一个用户列表，我们要向其中的每个用户发出问候：
def greet_users(names):
    """向列表中的每个用户发出简单的问候"""
    for name in names:
        msg = f"Hello, {name.title()}!"
        print(msg)

usernames = ['hannah', 'ty', 'margot']
greet_users(usernames)
运行结果
Hello, Hannah!
Hello, Ty!
Hello, Margot!
greet_users([‘hannah’, ‘ty’, ‘margot’]) #可以直接传列表
8.4 传递列表

幻灯片 48:
忆及 7.3 节中的代码：
8.4.1 在函数中修改列表（代码重构）
student_list = ['xiaoming', 'xiaohong', 'xiaoli']
in_class = []
while student_list:
    student = daqiandao.pop()
    print(f"{student.title()} 正在签到...")
    in_class.append(student)
print("\n以下同学已完成签到：")
for student in in_class:
    print(student.title())

幻灯片 49:
8.4.1 在函数中修改列表（代码重构）
def sign_in_students(student_list, in_class):
    """将等待签到的学生逐一签到，添加到已签到列表中"""
    while student_list:
        current_student = student_list.pop()
        print(f"{current_student.title()} 正在签到...")
        in_class.append(current_student)
def show_signed_in_students(in_class):
    """显示所有已完成签到的学生"""
    print("\n以下同学已完成签到：")
    for student in in_class:
        print(student.title())

幻灯片 50:
用函数调用代码：






8.4.1 在函数中修改列表（代码重构）
# 主程序
student_list = ['xiaoming', 'xiaohong', 'xiaoli']
in_class = []
sign_in_students(student_list, in_class)
show_signed_in_students(in_class)

幻灯片 51:
运行结果
Xiaoli 正在签到...
Xiaohong 正在签到...
Xiaoming 正在签到...

以下同学已完成签到：
Xiaoli
Xiaohong
Xiaoming
8.4.1 在函数中修改列表（代码重构）

幻灯片 52:
8.4.2 讨论

1.用函数重构代码有什么好处？

幻灯片 53:
8.4.2 讨论：为什么会需要用函数重构代码

1.代码分块
2.每次只关注一小块，方便思考，方便纠错。
3.若要添加功能，只关注代码小块即可。
4.用函数调用同一块代码，而不是重复写代码

幻灯片 54:
8.4.3 函数随堂练习
# 示例输出：
# Xiaotian 借出了图书...
# Xiaozhi 借出了图书...
# Xiaohua 借出了图书...

# 以下同学已成功借书：
# Xiaotian
# Xiaozhi
# Xiaohua
waiting_list = ['xiaohua', 'xiaozhi', 'xiaotian']
borrowed_list = []
# ✍️ 请写出两个函数：
# borrow_books(waiting_list, borrowed_list)
# show_borrowed_books(borrowed_list)

幻灯片 55:
8.4.4 函数小结


函数的定义
1
形参和实参
2
参数的传递方式
3
返回值
4
函数与循环的结合
5

幻灯片 56:
8.4.5 课后作业


1.下周一前提交超星学习通第八章（上）作业。

2.预习第八章（下），思考哪些知识是重点，哪些知识是难点。
