[{"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "glob", "kind": 6, "isExtraImport": true, "importPath": "glob", "description": "glob", "detail": "glob", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "sysconfig", "kind": 6, "isExtraImport": true, "importPath": "sysconfig", "description": "sysconfig", "detail": "sysconfig", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "winreg", "kind": 6, "isExtraImport": true, "importPath": "winreg", "description": "winreg", "detail": "winreg", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "BadZipFile", "importPath": "zipfile", "description": "zipfile", "isExtraImport": true, "detail": "zipfile", "documentation": {}}, {"label": "ZipFile", "importPath": "zipfile", "description": "zipfile", "isExtraImport": true, "detail": "zipfile", "documentation": {}}, {"label": "DocumentConverter", "importPath": "docling.document_converter", "description": "docling.document_converter", "isExtraImport": true, "detail": "docling.document_converter", "documentation": {}}, {"label": "PdfFormatOption", "importPath": "docling.document_converter", "description": "docling.document_converter", "isExtraImport": true, "detail": "docling.document_converter", "documentation": {}}, {"label": "PdfPipelineOptions", "importPath": "docling.datamodel.pipeline_options", "description": "docling.datamodel.pipeline_options", "isExtraImport": true, "detail": "docling.datamodel.pipeline_options", "documentation": {}}, {"label": "InputFormat", "importPath": "docling.datamodel.base_models", "description": "docling.datamodel.base_models", "isExtraImport": true, "detail": "docling.datamodel.base_models", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "<PERSON><PERSON>", "kind": 6, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "class Tee:\n    def __init__(self, file):\n        self.f = file\n    def write(self, what):\n        if self.f is not None:\n            try:\n                self.f.write(what.replace(\"\\n\", \"\\r\\n\"))\n            except OSError:\n                pass\n        tee_f.write(what)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "CopyTo", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def CopyTo(desc, src, dest):\n    import win32api\n    import win32con\n    while 1:\n        try:\n            win32api.CopyFile(src, dest, 0)\n            return\n        except win32api.error as details:\n            if details.winerror == 5:  # access denied - user not admin.\n                raise", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "LoadSystemModule", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def LoadSystemModule(lib_dir, modname):\n    # See if this is a debug build.\n    import importlib.machinery\n    import importlib.util\n    suffix = \"_d\" if \"_d.pyd\" in importlib.machinery.EXTENSION_SUFFIXES else \"\"\n    filename = \"%s%d%d%s.dll\" % (\n        modname,\n        sys.version_info.major,\n        sys.version_info.minor,\n        suffix,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "SetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def SetPyKeyVal(key_name, value_name, value):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.CreateKey(root_key, key_name)\n        try:\n            winreg.SetValueEx(my_key, value_name, 0, winreg.REG_SZ, value)\n            if verbose:\n                print(f\"-> {root_key_name}\\\\{key_name}[{value_name}]={value!r}\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "UnsetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def UnsetPyKeyVal(key_name, value_name, delete_key=False):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.OpenKey(root_key, key_name, 0, winreg.KEY_SET_VALUE)\n        try:\n            winreg.DeleteValue(my_key, value_name)\n            if verbose:\n                print(f\"-> DELETE {root_key_name}\\\\{key_name}[{value_name}]\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterCOMObjects", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterCOMObjects(register=True):\n    import win32com.server.register\n    if register:\n        func = win32com.server.register.RegisterClasses\n    else:\n        func = win32com.server.register.UnregisterClasses\n    flags = {}\n    if not verbose:\n        flags[\"quiet\"] = 1\n    for module, klass_name in com_modules:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterHelpFile", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterHelpFile(register=True, lib_dir=None):\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    if register:\n        # Register the .chm help file.\n        chm_file = os.path.join(lib_dir, \"PyWin32.chm\")\n        if os.path.isfile(chm_file):\n            # This isn't recursive, so if 'Help' doesn't exist, we croak\n            SetPyKeyVal(\"Help\", None, None)\n            SetPyKeyVal(\"Help\\\\Pythonwin Reference\", None, chm_file)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterPyt<PERSON><PERSON>", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterPythonwin(register=True, lib_dir=None):\n    \"\"\"Add (or remove) Pythonwin to context menu for python scripts.\n    ??? Should probably also add Edit command for pys files also.\n    Also need to remove these keys on uninstall, but there's no function\n        like file_created to add registry entries to uninstall log ???\n    \"\"\"\n    import os\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    classes_root = get_root_hkey()", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_shortcuts_folder", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_shortcuts_folder():\n    if get_root_hkey() == winreg.HKEY_LOCAL_MACHINE:\n        try:\n            fldr = get_special_folder_path(\"CSIDL_COMMON_PROGRAMS\")\n        except OSError:\n            # No CSIDL_COMMON_PROGRAMS on this platform\n            fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")\n    else:\n        # non-admin install - always goes in this user's start menu.\n        fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_system_dir", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_system_dir():\n    import win32api  # we assume this exists.\n    try:\n        import pythoncom\n        import win32process\n        from win32com.shell import shell, shellcon\n        try:\n            if win32process.IsWow64Process():\n                return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEMX86)\n            return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEM)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "fixup_dbi", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def fixup_dbi():\n    # We used to have a dbi.pyd with our .pyd files, but now have a .py file.\n    # If the user didn't uninstall, they will find the .pyd which will cause\n    # problems - so handle that.\n    import win32api\n    import win32con\n    pyd_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi.pyd\")\n    pyd_d_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi_d.pyd\")\n    py_name = os.path.join(os.path.dirname(win32con.__file__), \"dbi.py\")\n    for this_pyd in (pyd_name, pyd_d_name):", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "install", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def install(lib_dir):\n    import traceback\n    # The .pth file is now installed as a regular file.\n    # Create the .pth file in the site-packages dir, and use only relative paths\n    # We used to write a .pth directly to sys.prefix - clobber it.\n    if os.path.isfile(os.path.join(sys.prefix, \"pywin32.pth\")):\n        os.unlink(os.path.join(sys.prefix, \"pywin32.pth\"))\n    # The .pth may be new and therefore not loaded in this session.\n    # Setup the paths just in case.\n    for name in \"win32 win32\\\\lib Pythonwin\".split():", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "uninstall", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def uninstall(lib_dir):\n    # First ensure our system modules are loaded from pywin32_system, so\n    # we can remove the ones we copied...\n    LoadSystemModule(lib_dir, \"pywintypes\")\n    LoadSystemModule(lib_dir, \"pythoncom\")\n    try:\n        RegisterCOMObjects(False)\n    except Exception as why:\n        print(f\"Failed to unregister COM objects: {why}\")\n    try:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verify_destination", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def verify_destination(location):\n    if not os.path.isdir(location):\n        raise argparse.ArgumentTypeError(f'Path \"{location}\" does not exist!')\n    return location\ndef main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,\n        description=\"\"\"A post-install script for the pywin32 extensions.\n    * Typical usage:\n    > python pywin32_postinstall.py -install", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,\n        description=\"\"\"A post-install script for the pywin32 extensions.\n    * Typical usage:\n    > python pywin32_postinstall.py -install\n    If you installed pywin32 via a .exe installer, this should be run\n    automatically after installation, but if it fails you can run it again.\n    If you installed pywin32 via PIP, you almost certainly need to run this to\n    setup the environment correctly.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "tee_f", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "tee_f = open(os.path.join(tempfile.gettempdir(), \"pywin32_postinstall.log\"), \"w\")\nclass Tee:\n    def __init__(self, file):\n        self.f = file\n    def write(self, what):\n        if self.f is not None:\n            try:\n                self.f.write(what.replace(\"\\n\", \"\\r\\n\"))\n            except OSError:\n                pass", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stderr", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stderr = <PERSON><PERSON>(sys.stderr)\nsys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stdout", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "com_modules", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "com_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0\n# Verbosity of output messages.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "silent", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "silent = 0\n# Verbosity of output messages.\nverbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ntry:\n    # When this script is run from inside the bdist_wininst installer,\n    # file_created() and directory_created() are additional builtin\n    # functions which write lines to PythonXX\\pywin32-install.log. This is\n    # a list of actions for the uninstaller, the format is inspired by what\n    # the Wise installer also creates.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verbose", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "verbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ntry:\n    # When this script is run from inside the bdist_wininst installer,\n    # file_created() and directory_created() are additional builtin\n    # functions which write lines to PythonXX\\pywin32-install.log. This is\n    # a list of actions for the uninstaller, the format is inspired by what\n    # the <PERSON> installer also creates.\n    file_created  # type: ignore[used-before-def]\n    # 3.10 stopped supporting bdist_wininst, but we can still build them with 3.9.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "root_key_name", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "root_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ntry:\n    # When this script is run from inside the bdist_wininst installer,\n    # file_created() and directory_created() are additional builtin\n    # functions which write lines to PythonXX\\pywin32-install.log. This is\n    # a list of actions for the uninstaller, the format is inspired by what\n    # the Wise installer also creates.\n    file_created  # type: ignore[used-before-def]\n    # 3.10 stopped supporting bdist_wininst, but we can still build them with 3.9.\n    # This can be kept until Python 3.9 or exe installers support is dropped.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "run_test", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()\n    result = subprocess.run(cmd, check=False, cwd=dirname)\n    print(f\"*** Test script '{script}' exited with {result.returncode}\")\n    sys.stdout.flush()\n    if result.returncode:", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "find_and_run", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def find_and_run(possible_locations, extras):\n    for maybe in possible_locations:\n        if os.path.isfile(maybe):\n            run_test(maybe, extras)\n            break\n    else:\n        raise RuntimeError(\n            \"Failed to locate a test script in one of %s\" % possible_locations\n        )\ndef main():", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def main():\n    import argparse\n    code_directories = [this_dir] + site_packages\n    parser = argparse.ArgumentParser(\n        description=\"A script to trigger tests in all subprojects of PyWin32.\"\n    )\n    parser.add_argument(\n        \"-no-user-interaction\",\n        default=False,\n        action=\"store_true\",", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "this_dir", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "this_dir = os.path.dirname(__file__)\nsite_packages = [\n    site.getusersitepackages(),\n] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "site_packages", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "site_packages = [\n    site.getusersitepackages(),\n] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "failures", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "failures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "extract_file", "kind": 2, "importPath": ".venv.Scripts.vba_extract", "description": ".venv.Scripts.vba_extract", "peekOfCode": "def extract_file(xlsm_zip, filename):\n    # Extract a single file from an Excel xlsm macro file.\n    data = xlsm_zip.read(\"xl/\" + filename)\n    # Write the data to a local file.\n    file = open(filename, \"wb\")\n    file.write(data)\n    file.close()\n# The VBA project file and project signature file we want to extract.\nvba_filename = \"vbaProject.bin\"\nvba_signature_filename = \"vbaProjectSignature.bin\"", "detail": ".venv.Scripts.vba_extract", "documentation": {}}, {"label": "vba_filename", "kind": 5, "importPath": ".venv.Scripts.vba_extract", "description": ".venv.Scripts.vba_extract", "peekOfCode": "vba_filename = \"vbaProject.bin\"\nvba_signature_filename = \"vbaProjectSignature.bin\"\n# Get the xlsm file name from the commandline.\nif len(sys.argv) > 1:\n    xlsm_file = sys.argv[1]\nelse:\n    print(\n        \"\\nUtility to extract a vbaProject.bin binary from an Excel 2007+ \"\n        \"xlsm macro file for insertion into an XlsxWriter file.\\n\"\n        \"If the macros are digitally signed, extracts also a vbaProjectSignature.bin \"", "detail": ".venv.Scripts.vba_extract", "documentation": {}}, {"label": "vba_signature_filename", "kind": 5, "importPath": ".venv.Scripts.vba_extract", "description": ".venv.Scripts.vba_extract", "peekOfCode": "vba_signature_filename = \"vbaProjectSignature.bin\"\n# Get the xlsm file name from the commandline.\nif len(sys.argv) > 1:\n    xlsm_file = sys.argv[1]\nelse:\n    print(\n        \"\\nUtility to extract a vbaProject.bin binary from an Excel 2007+ \"\n        \"xlsm macro file for insertion into an XlsxWriter file.\\n\"\n        \"If the macros are digitally signed, extracts also a vbaProjectSignature.bin \"\n        \"file.\\n\"", "detail": ".venv.Scripts.vba_extract", "documentation": {}}, {"label": "source", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "source = r\"C:\\Users\\<USER>\\Desktop\\teaching_material_generation\\python编程从入门到实践.pdf\"\n# 设置输出目录和文件路径\noutput_dir = Path(r\"C:\\Users\\<USER>\\Desktop\\teaching_material_generation\")\noutput_dir.mkdir(exist_ok=True)  # 确保目录存在\noutput_file = output_dir / \"python编程从入门到实践.md\"\n# 配置PDF处理选项\npipeline_options = PdfPipelineOptions(\n    do_ocr=False,  # 关闭OCR，因为PDF本身包含可选择的文本\n    do_table_structure=True  # 保持表格结构识别开启\n)", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "output_dir", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "output_dir = Path(r\"C:\\Users\\<USER>\\Desktop\\teaching_material_generation\")\noutput_dir.mkdir(exist_ok=True)  # 确保目录存在\noutput_file = output_dir / \"python编程从入门到实践.md\"\n# 配置PDF处理选项\npipeline_options = PdfPipelineOptions(\n    do_ocr=False,  # 关闭OCR，因为PDF本身包含可选择的文本\n    do_table_structure=True  # 保持表格结构识别开启\n)\n# 创建文档转换器，添加PDF特定配置\nconverter = DocumentConverter(", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "output_file", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "output_file = output_dir / \"python编程从入门到实践.md\"\n# 配置PDF处理选项\npipeline_options = PdfPipelineOptions(\n    do_ocr=False,  # 关闭OCR，因为PDF本身包含可选择的文本\n    do_table_structure=True  # 保持表格结构识别开启\n)\n# 创建文档转换器，添加PDF特定配置\nconverter = DocumentConverter(\n    format_options={\n        InputFormat.PDF: PdfFormatOption(", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "pipeline_options", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "pipeline_options = PdfPipelineOptions(\n    do_ocr=False,  # 关闭OCR，因为PDF本身包含可选择的文本\n    do_table_structure=True  # 保持表格结构识别开启\n)\n# 创建文档转换器，添加PDF特定配置\nconverter = DocumentConverter(\n    format_options={\n        InputFormat.PDF: PdfFormatOption(\n            pipeline_options=pipeline_options\n        )", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "converter", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "converter = DocumentConverter(\n    format_options={\n        InputFormat.PDF: PdfFormatOption(\n            pipeline_options=pipeline_options\n        )\n    }\n)\n# 记录开始时间\nstart_time = time.time()\n# 转换文档", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "start_time", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "start_time = time.time()\n# 转换文档\nprint(\"开始转换文档...\")\nresult = converter.convert(source)\n# 计算并显示耗时\nend_time = time.time()\nprint(f\"\\n转换完成，耗时: {end_time - start_time:.2f} 秒\")\n# 将结果保存为markdown文件\nwith open(output_file, \"w\", encoding=\"utf-8\") as f:\n    f.write(result.document.export_to_markdown())", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "result", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "result = converter.convert(source)\n# 计算并显示耗时\nend_time = time.time()\nprint(f\"\\n转换完成，耗时: {end_time - start_time:.2f} 秒\")\n# 将结果保存为markdown文件\nwith open(output_file, \"w\", encoding=\"utf-8\") as f:\n    f.write(result.document.export_to_markdown())\nprint(f\"结果已保存至: {output_file}\")", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "end_time", "kind": 5, "importPath": "cursor ignore.pdf_to_ai_ready", "description": "cursor ignore.pdf_to_ai_ready", "peekOfCode": "end_time = time.time()\nprint(f\"\\n转换完成，耗时: {end_time - start_time:.2f} 秒\")\n# 将结果保存为markdown文件\nwith open(output_file, \"w\", encoding=\"utf-8\") as f:\n    f.write(result.document.export_to_markdown())\nprint(f\"结果已保存至: {output_file}\")", "detail": "cursor ignore.pdf_to_ai_ready", "documentation": {}}, {"label": "introduce", "kind": 2, "importPath": "demo", "description": "demo", "peekOfCode": "def introduce(name, hobby=\"编程\"):\n    print(f\"我是{name}，我喜欢{hobby}\")\n# 第二步：三种方式调用函数\n# （1）只传入一个参数\nintroduce(\"小明\")\n# （2）位置传参两个参数\nintroduce(\"小红\", \"打篮球\")\n# （3）关键字传参两个参数\nintroduce(hobby=\"读书\", name=\"小刚\")", "detail": "demo", "documentation": {}}]