幻灯片 1:
Python程序设计
第六章 字典

教师：冯子晋

幻灯片 2:
一个简单的字典
01
使用字典
02
嵌套
04
小结
05
遍历字典
03
第 6 章 字典


幻灯片 3:

导入：为什么需要字典

'姓名' -> '张三'

'年龄' -> 25

'城市' -> '北京'
用列表存储数据时难以理解每个位置含义

字典让数据有“标签”，更清晰

0 -> '张三'

1 -> 25

2 -> '北京'


幻灯片 4:

6.1 一个简单的字典
键               值   

person = {
'name' :  '张三',
'age'  :   5,
'city' :  '北京'
}

幻灯片 5:


6.2.1 访问字典中的值
运行结果：
张三
person = {'name': '张三', 'age': 5 , 'city': '北京'}

print(person['name'])

幻灯片 6:


6.2.2 添加键值对


运行结果：
女
person= {'name': '张三', 'age': 5, 'city': '北京'}
person['gender'] = '女'
print(person['gender'])  # 输出 '女'
添加键值对：dictname['新键'] = '值'。

幻灯片 7:


6.2.3 从创建一个空字典开始
常见实践：创建空字典再添加元素
运行结果
{'name': '张三', 'age'  : 5 , 'city'  : '北京'}
person = {}				# 创建空字典
person['name'] = '张三'		# 添加键值对 name: 张三
person['age'] = 5			# 添加键值对 age: 5
person['city'] ='北京'			# 添加键值对 city: 北京
print(person)				# 打印整个字典内容

幻灯片 8:


6.2.4 修改字典中的值
运行结果
贵阳
person = {'name': '张三', 'age': 5, 'city': '北京'}
person['city'] = '贵阳'
print(person['city'])
修改值：dictname['键'] = '新值'。


幻灯片 9:


6.2.5 删除键值对


运行结果
{'name': '张三', 'age': 5}
person = {'name': '张三', 'age': 5, 'city': '北京'}
del person['city']   # 删除 'city' 这个键值对
print(person)
删除键值对：del dictname['键']。

幻灯片 10:


6.2.6 使用键值对的常见情景
一个对象的多种信息
多个对象的同一种信息
student = {
    'name': '小明',
    'age': 18,
    'city': '广州'
}
favorite_subjects = {
    '小红': '语文',
    '小刚': '数学',
    '小丽': '英语',
    '小杰': '语文'
}

幻灯片 11:


6.2.7 使用 get() 来访问值
访问不存在的键，会给出键错误（KeyError）
运行结果
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\教学准入考核提交资料\demo.py", line 6, in <module>
    print(student['school'])  # ❌ 这个键不存在
          ~~~~~~~^^^^^^^^^^
KeyError: 'school'
student = {'name': '小明', 'grade': 'A'}
print(student['school'])  # ❌ 这个键不存在

幻灯片 12:


6.2.7 使用 get() 来访问值
用 get() 方法，访问不存在的键，不报错
student = {'name': '小明', 'age': 18, 'city': '广州'}

print(student.get('school'))            # 输出 None
print(student.get('school', '未填写'))  # 输出 '未填写'
运行结果
None
未填写

幻灯片 13:


6.2.8 讨论：用字典还是列表

题目 1：记录今天班里所有学生的出勤情况（姓名 -> 是否到场）
题目 2：记录你最喜欢的 5 种水果的名字
题目 3：存储一个游戏角色的信息，包括名字、等级、经验值、血量
题目 4：保存班级中所有人的身高，计算平均值

幻灯片 14:


6.2.8 讨论：用字典还是列表

# 题目 1：记录今天班里所有学生的出勤情况（姓名 -> 是否到场）
attendance= {
    '张小明': True,
    '李小红': False,
    '王大伟': True
}

幻灯片 15:


6.2.8 讨论：用字典还是列表

# 题目 2：记录你最喜欢的 5 种水果的名字
# 用列表记录水果
fruits = ['苹果', '香蕉', '草莓', '葡萄', '橙子']

print(fruits[0])  # 输出：苹果

幻灯片 16:


6.2.8 讨论：用字典还是列表

# 题目 3：存储一个游戏角色的信息，包括名字、等级、经验值、血量
# 用字典表示角色信息
player = {
    'name': '李华',
    'level': 10,
    'exp': 2300,
    'hp': 95
}

幻灯片 17:


6.2.8 讨论：用字典还是列表

# 题目 4：保存班级中所有人的身高，计算平均值
# 用列表保存身高
heights = [160, 172, 168, 175, 180]

average = sum(heights) /len(heights)
print(f"平均身高是 {average} 厘米") # 输出示例:平均身高是171.0厘米

幻灯片 18:


6.2.9 回顾字典

1.用什么方法访问字典中的值？

2.如何添加新的键值对？

3.用哪种方法可以避免KeyError？



幻灯片 19:


6.3.1 遍历所有的键值对
d.items() ：返回所有键值对的元组视图。
运行结果
小红 喜欢 语文。
小刚 喜欢 数学。
小丽 喜欢 英语。
favorite_subjects = {
    '小红': '语文',
    '小刚': '数学',
    '小丽': '英语'
}

for name, subject in favorite_subjects.items():
    print(f"{name} 喜欢 {subject}。")

幻灯片 20:


6.3.2 遍历字典所有键
d.keys() ：返回所有键的列表视图。
favorite_subjects = {
    '小红': '语文',
    '小刚': '数学',
    '小丽': '英语'
}

for name in favorite_subjects.keys():
    subject = favorite_subjects[name]
    print(f"{name} 喜欢 {subject}。")
运行结果
小红 喜欢 语文。
小刚 喜欢 数学。
小丽 喜欢 英语。

幻灯片 21:



6.3.3 按特定的顺序遍历字典中的所有键
sorted(d.keys())  ：返回所有键按字母排序后的列表视图。
运行结果
小刚 喜欢 数学。
小红 喜欢 语文。
favorite_subjects = {
    '小红': '语文',
    '小刚': '数学'
}

for name in sorted(favorite_subjects.keys()):
    subject = favorite_subjects[name]
    print(f"{name} 喜欢 {subject}。")

幻灯片 22:


6.3.4 遍历字典中的所有值
d.values()：返回所有值的列表视图。
运行结果
语文
数学
英语
favorite_subjects = {
    '小红': '语文',
    '小刚': '数学',
    '小丽': '英语'
}

for subject in favorite_subjects.valuaes():
    print(subject)

幻灯片 23:


6.4.1 嵌套：字典列表
字典可以作为列表的元素
运行结果
{'name': '小红', 'score': 85}
{'name': '小刚', 'score': 90}
{'name': '小丽', 'score': 95}
student_0 = {'name': '小红', 'score': 85}
student_1 = {'name': '小刚', 'score': 90}
student_2 = {'name': '小丽', 'score': 95}

students = [student_0, student_1, student_2]

for student in students:
    print(student)

幻灯片 24:


6.4.2 嵌套：在字典中存储列表
列表可以作为键值对的值
运行结果
小明的课外兴趣有：
篮球
画画
下围棋
student = {
    'name': '小明',
    'hobbies': ['篮球', '画画', '下围棋']
}

print(f"{student['name']}的课外兴趣有：")
for hobby in student['hobbies']:
    print(f"\t{hobby}")

幻灯片 25:


6.4.3 嵌套：在字典中存储字典
字典作为键值对的值
family = {
    'dad': {
        'name': '李大志',
        'age': 45,
        'hobby': '书法',
    },
    'mom': {
        'name': '王美丽',
        'age': 42,
        'hobby': '跳广场舞',
    },
}
for role, info in family.items():
    print(
        f"{info['name']} 是{role}，今年 {info['age']} 岁，喜欢 {info['hobby']}。")
运行结果
李大志 是 dad，今年45岁，喜欢书法
王美丽 是 mom，今年42岁，喜欢跳广场舞

幻灯片 26:


6.4.4 随堂小测：在字典中存储字典
练习题：记录三位学生的语文成绩并计算平均分
# 学生语文成绩
scores = {'小红': 85,'小刚': 90,'小丽': 95}
total = 0

# 遍历并输出每位学生的成绩
for name, score in scores.items():
    print(f"{name} 的语文成绩是 {score} 分")
    total += score

# 计算平均成绩
average = total/len(scores)
print(f"语文平均成绩是 {average} 分")

幻灯片 27:

6.5 小结
字典由键值对组成,键必须唯一,而值不必。
字典基础:


字典是无序的。
键必须是不可变类型,如字符串、数字或元组。
02
04
可以通过dictname['键']访问字典中的值。
01
03

幻灯片 28:


6.5 小结
操作字典：

添加键值对：dictname['新键'] = '值'。
修改值：dictname['键'] = '新值'。
删除键值对：del dictname['键']。
使用get()避免访问不存在的键导致的错误，可指定默认返回值。


幻灯片 29:


6.5 小结
遍历字典：

使用.items()遍历键值对。
使用.keys()遍历所有键。
使用.values()遍历所有值。
可以对遍历的结果进行排序，如使用sorted()。



幻灯片 30:


6.5 课后习题


1.超星学习通所有习题，下周一前完成
2.预习第7章，思考哪些是重点难点
