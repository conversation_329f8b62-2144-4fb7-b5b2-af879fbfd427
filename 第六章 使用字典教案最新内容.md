# Python 程序设计课程教学设计方案

## 基本信息

- **课程名称**：Python 程序设计
- **授课章节**：第六章 字典
- **授课班级**：软件工程专业
- **授课时间**：45 分钟（1 学时）
- **授课教师**：冯子晋
- **授课地点**：计算机实验室

## 教学分析

### 学情分析

学生已掌握 Python 基础语法、列表、循环等知识，具备一定的编程基础，但对数据结构的选择和应用还需要进一步指导。学生对实际应用场景的理解需要通过具体案例来加强。

### 教材分析

本章是 Python 数据结构的重要组成部分，字典作为键值对数据结构，在实际编程中应用广泛。内容包括字典的创建、操作、遍历和嵌套应用，是后续学习的重要基础。

## 教学目标

### 知识目标

1. 理解字典的概念和特点，掌握键值对的数据结构
2. 掌握字典的创建、访问、添加、修改、删除等基本操作
3. 熟练使用字典的遍历方法：keys()、values()、items()
4. 理解字典嵌套的概念和应用场景

### 能力目标

1. 能够根据实际需求选择合适的数据结构（字典 vs 列表）
2. 能够设计和实现基于字典的数据管理程序
3. 能够处理复杂的嵌套数据结构
4. 能够使用 get()方法安全地访问字典数据

### 素质目标

1. 培养学生的逻辑思维能力和抽象思维能力
2. 提高学生分析问题和解决问题的能力
3. 培养学生良好的编程习惯和代码规范意识
4. 增强学生的团队协作和交流表达能力

## 教学重点与难点

### 教学重点

1. 字典的基本概念和键值对结构的理解
2. 字典基本操作的语法和使用方法
3. 字典遍历的三种方法及其应用场景
4. 字典与列表在不同场景下的选择原则

### 教学难点

1. 嵌套数据结构的设计和操作方法
2. get()方法在避免 KeyError 中的应用
3. 复杂数据结构的遍历和处理逻辑
4. 实际问题中数据结构的合理选择

## 教学方法与手段

### 教学方法

1. **讲授法**：系统讲解字典的概念、特点和操作方法
2. **演示法**：通过实际代码演示展示字典的各种操作过程
3. **案例教学法**：通过实际应用案例帮助学生理解字典的使用场景
4. **讨论法**：引导学生讨论和分析不同数据结构的适用场景
5. **实践教学法**：通过编程练习让学生动手实践，加深理解

### 教学手段

1. **多媒体课件**：使用 PPT 展示概念和代码示例
2. **编程环境**：使用 Python IDE 进行实时代码演示
3. **互动平台**：利用课堂互动系统进行实时问答
4. **在线资源**：结合超星学习通平台进行课后练习

## 教学过程设计

### 第一环节：课程导入（5 分钟）

#### 教学内容

**问题情境创设**：如何更好地存储和管理有标签的数据？

#### 教学活动

1. **复习回顾**（2 分钟）

   - 回顾列表的特点和使用方法
   - 提问：用列表存储个人信息有什么不便之处？

2. **问题引入**（3 分钟）
   - 展示用列表存储个人信息的代码：
   ```python
   person_info = ['张三', 25, '北京']  # 难以理解每个位置的含义
   ```
   - 分析问题：索引 0、1、2 分别代表什么？如何让数据更有意义？
   - 引出字典概念：让数据有"标签"，更清晰易懂

#### 设计意图

通过对比列表存储数据的局限性，激发学生学习字典的兴趣，为新知识的学习做好铺垫。

### 第二环节：新知探究（20 分钟）

#### 教学内容一：字典的基本概念（4 分钟）

**知识点**：字典的定义、特点和基本结构

**教学活动**：

1. **概念讲解**（2 分钟）

   - 字典的定义：由键值对组成的可变数据类型
   - 字典的特点：键唯一、无序、可变
   - 键值对结构：键(key) -> 值(value)

2. **代码演示**（2 分钟）
   ```python
   # 创建一个简单的字典
   person = {
       'name': '张三',
       'age': 5,
       'city': '北京'
   }
   ```

**教学方法**：讲授法 + 演示法
**设计意图**：建立字典的基本概念，为后续学习奠定基础

#### 教学内容二：字典的基本操作（12 分钟）

**知识点**：字典的访问、添加、修改、删除操作

**教学活动**：

1. **访问字典中的值**（2 分钟）

   ```python
   person = {'name': '张三', 'age': 5, 'city': '北京'}
   print(person['name'])  # 输出：张三
   ```

2. **添加键值对**（2 分钟）

   ```python
   person = {'name': '张三', 'age': 5, 'city': '北京'}
   person['gender'] = '女'
   print(person['gender'])  # 输出：女
   ```

3. **从空字典开始**（3 分钟）

   ```python
   person = {}                    # 创建空字典
   person['name'] = '张三'        # 添加键值对 name: 张三
   person['age'] = 5              # 添加键值对 age: 5
   person['city'] = '北京'        # 添加键值对 city: 北京
   print(person)                  # 打印整个字典内容
   ```

4. **修改字典中的值**（2 分钟）

   ```python
   person = {'name': '张三', 'age': 5, 'city': '北京'}
   person['city'] = '贵阳'
   print(person['city'])  # 输出：贵阳
   ```

5. **删除键值对**（2 分钟）

   ```python
   person = {'name': '张三', 'age': 5, 'city': '北京'}
   del person['city']   # 删除 'city' 这个键值对
   print(person)
   ```

6. **使用键值对的常见情景**（1 分钟）
   - 一个对象的多种信息：学生信息字典
   - 多个对象的同一种信息：学生喜欢的科目字典

**教学方法**：演示法 + 实践法
**设计意图**：通过逐步演示让学生掌握字典的基本操作方法

#### 教学内容三：使用 get()方法安全访问（4 分钟）

**知识点**：使用 get()方法避免 KeyError

**教学活动**：

1. **问题展示**（2 分钟）

   ```python
   student = {'name': '小明', 'grade': 'A'}
   print(student['school'])  # KeyError: 'school'
   ```

2. **解决方案**（2 分钟）
   ```python
   student = {'name': '小明', 'age': 18, 'city': '广州'}
   print(student.get('school'))            # 输出 None
   print(student.get('school', '未填写'))  # 输出 '未填写'
   ```

**设计意图**：培养学生安全编程的意识

#### 教学内容四：回顾字典基础（0 分钟）

**知识点**：字典基础知识回顾

**教学活动**：

快速回顾问题（穿插在前面内容中）：

1. 用什么方法访问字典中的值？
2. 如何添加新的键值对？
3. 用哪种方法可以避免 KeyError？

**设计意图**：巩固基础知识，为深入学习做准备

### 第三环节：深入应用（15 分钟）

#### 教学内容一：数据结构选择讨论（8 分钟）

**知识点**：字典 vs 列表的选择原则

**教学活动**：

1. **案例分析**（6 分钟）

   - 题目 1：记录班里所有学生的出勤情况（姓名 -> 是否到场）→ 字典

   ```python
   attendance = {
       '张小明': True,
       '李小红': False,
       '王大伟': True
   }
   ```

   - 题目 2：记录最喜欢的 5 种水果的名字 → 列表

   ```python
   fruits = ['苹果', '香蕉', '草莓', '葡萄', '橙子']
   print(fruits[0])  # 输出：苹果
   ```

   - 题目 3：存储游戏角色信息（名字、等级、经验值、血量）→ 字典

   ```python
   player = {
       'name': '李华',
       'level': 10,
       'exp': 2300,
       'hp': 95
   }
   ```

   - 题目 4：保存班级所有人的身高，计算平均值 → 列表

   ```python
   heights = [160, 172, 168, 175, 180]
   average = sum(heights) / len(heights)
   print(f"平均身高是 {average} 厘米")
   ```

2. **总结规律**（2 分钟）
   - 需要标签化数据时选择字典
   - 需要顺序存储或数值计算时选择列表

**教学方法**：讨论法 + 案例法
**设计意图**：培养学生选择合适数据结构的能力

#### 教学内容二：字典的遍历方法（7 分钟）

**知识点**：字典的三种遍历方式

**教学活动**：

1. **遍历所有键值对**（3 分钟）

   ```python
   favorite_subjects = {'小红': '语文', '小刚': '数学', '小丽': '英语'}
   for name, subject in favorite_subjects.items():
       print(f"{name} 喜欢 {subject}。")
   ```

2. **遍历所有键**（2 分钟）

   ```python
   for name in favorite_subjects.keys():
       subject = favorite_subjects[name]
       print(f"{name} 喜欢 {subject}。")
   ```

3. **按特定顺序遍历**（1 分钟）

   ```python
   for name in sorted(favorite_subjects.keys()):
       print(f"{name} 喜欢 {favorite_subjects[name]}。")
   ```

4. **遍历所有值**（1 分钟）
   ```python
   for subject in favorite_subjects.values():
       print(subject)
   ```

**教学方法**：演示法 + 对比法
**设计意图**：让学生掌握不同遍历方式的特点和适用场景

### 第四环节：嵌套数据结构（3 分钟）

#### 教学内容：字典与列表的嵌套应用

**知识点**：字典与列表的嵌套应用

**教学活动**：

1. **字典列表**（1 分钟）

   ```python
   student_0 = {'name': '小红', 'score': 85}
   student_1 = {'name': '小刚', 'score': 90}
   student_2 = {'name': '小丽', 'score': 95}
   students = [student_0, student_1, student_2]
   for student in students:
       print(student)
   ```

2. **在字典中存储列表**（1 分钟）

   ```python
   student = {
       'name': '小明',
       'hobbies': ['篮球', '画画', '下围棋']
   }
   print(f"{student['name']}的课外兴趣有：")
   for hobby in student['hobbies']:
       print(f"\t{hobby}")
   ```

3. **在字典中存储字典**（1 分钟）
   ```python
   family = {
       'dad': {'name': '李大志', 'age': 45, 'hobby': '书法'},
       'mom': {'name': '王美丽', 'age': 42, 'hobby': '跳广场舞'}
   }
   for role, info in family.items():
       print(f"{info['name']} 是{role}，今年 {info['age']} 岁，喜欢 {info['hobby']}。")
   ```

**教学方法**：演示法 + 实践法
**设计意图**：提高学生处理复杂数据结构的能力

### 第五环节：课堂练习与小结（2 分钟）

#### 教学活动

1. **随堂小测**（1 分钟）

   练习题：记录三位学生的语文成绩并计算平均分

   ```python
   # 学生语文成绩
   scores = {'小红': 85, '小刚': 90, '小丽': 95}
   total = 0

   # 遍历并输出每位学生的成绩
   for name, score in scores.items():
       print(f"{name} 的语文成绩是 {score} 分")
       total += score

   # 计算平均成绩
   average = total / len(scores)
   print(f"语文平均成绩是 {average} 分")
   ```

2. **知识小结**（1 分钟）

   - 字典基础：键值对组成，键必须唯一，字典是无序的
   - 操作字典：添加、修改、删除键值对，使用 get()避免错误
   - 遍历字典：使用.items()、.keys()、.values()方法

**设计意图**：帮助学生构建完整的知识体系

## 教学评价设计

### 形成性评价

1. **课堂提问**：通过随机提问检查学生对概念的理解
2. **编程练习**：观察学生编程过程中的问题和解决方法
3. **小组讨论**：评价学生在数据结构选择讨论中的参与度和观点质量
4. **即时反馈**：通过课堂互动系统收集学生的理解程度

### 总结性评价

1. **课堂练习完成情况**：评价学生对字典操作的掌握程度
2. **案例分析能力**：评价学生选择合适数据结构的能力
3. **代码规范性**：评价学生编程习惯和代码质量
4. **课后作业质量**：通过作业完成情况评价学习效果

## 教学资源

### 硬件资源

- 计算机实验室（每人一台电脑）
- 投影设备和音响系统
- 网络环境支持

### 软件资源

- Python 3.x 开发环境（推荐 PyCharm 或 VS Code）
- 超星学习通平台
- 课堂互动系统

### 教学材料

- PPT 课件
- 代码示例文件
- 练习题库
- 参考资料链接

## 参考资料

### 主要教材

1. 《Python 编程：从入门到实践》第 6 章 - Eric Matthes 著
2. 《Python 核心编程》字典相关章节

### 辅助资料

1. Python 官方文档：字典类型说明
2. 菜鸟教程：Python 字典
3. 廖雪峰 Python 教程：字典部分
4. 相关在线编程练习平台（LeetCode、牛客网等）

## 作业布置

### 必做作业

1. **基础练习**：

   - 完成超星学习通第六章所有习题（下周一前完成）
   - 编写程序：创建个人信息字典，实现信息的增删改查

2. **应用练习**：
   - 设计一个简单的学生成绩管理系统
   - 要求：使用字典存储学生信息，包含姓名、学号、各科成绩
   - 功能：添加学生、查询成绩、修改成绩、计算平均分

### 选做作业

1. **拓展练习**：
   - 研究 Python 字典的高级用法（如字典推导式）
   - 尝试使用嵌套字典设计一个班级管理系统

### 预习要求

1. **下节课预习**：

   - 预习第 7 章内容，思考哪些是重点难点
   - 思考字典和其他数据结构结合的应用场景

2. **思考问题**：
   - 字典在实际项目中的应用场景有哪些？
   - 如何设计更复杂的嵌套数据结构？

## 教学反思

### 教学效果预期

1. **知识掌握**：学生能够熟练使用字典进行数据存储和操作
2. **能力提升**：学生具备选择合适数据结构的能力
3. **思维发展**：培养学生的抽象思维和问题分解能力

### 可能遇到的问题及对策

1. **概念理解困难**：通过具体实例和对比帮助理解
2. **语法错误频繁**：加强代码演示和练习指导
3. **嵌套结构复杂**：采用循序渐进的方式，从简单到复杂

### 改进方向

1. 增加更多实际应用案例
2. 加强学生动手实践的时间
3. 完善课后辅导和答疑机制

---

**备注**：本教案设计遵循"以学生为中心"的教学理念，注重理论与实践相结合，通过多种教学方法激发学生学习兴趣，培养学生的编程思维和实际应用能力。
