# Python 程序设计课程教学设计方案

## 基本信息

- **课程名称**：Python 程序设计
- **授课章节**：第八章 函数（上）
- **授课班级**：软件工程专业
- **授课时间**：45 分钟（1 学时）
- **授课教师**：冯子晋
- **授课地点**：计算机实验室

## 教学分析

### 学情分析

学生已掌握 Python 基础语法、数据结构（列表、字典）、循环控制等知识，具备一定的编程基础。但对模块化编程思维还需要培养，对代码重用和函数设计的理解需要通过实践来加强。

### 教材分析

本章是 Python 程序设计的核心内容，函数作为程序模块化的基础，是提高代码质量和开发效率的重要工具。内容包括函数定义、参数传递、返回值处理和实际应用，是后续面向对象编程的重要基础。

## 教学目标

### 知识目标

1. 理解函数的概念和作用，掌握函数的定义语法
2. 掌握形参与实参的概念，理解参数传递机制
3. 熟练使用位置实参、关键字实参、默认值参数
4. 掌握函数返回值的设计和处理方法
5. 理解函数与列表等数据结构的结合应用

### 能力目标

1. 能够设计和实现功能明确的函数模块
2. 能够选择合适的参数传递方式
3. 能够处理不同类型的返回值
4. 能够使用函数进行代码重构和优化
5. 能够结合函数处理复杂的数据结构

### 素质目标

1. 培养学生的模块化编程思维和系统设计能力
2. 提高学生的代码重用意识和软件工程素养
3. 培养学生良好的编程习惯和代码规范意识
4. 增强学生解决复杂问题的分解和抽象能力

## 教学重点与难点

### 教学重点

1. 函数的定义语法和调用方法
2. 形参与实参的概念和使用规则
3. 三种参数传递方式的特点和应用
4. 函数返回值的设计原则和处理技巧
5. 函数在代码重构中的实际应用

### 教学难点

1. 参数传递方式的选择和混合使用
2. 复杂返回值的设计和处理
3. 函数接口的设计和优化
4. 函数与数据结构的深度结合应用
5. 代码重构的思路和实践方法

## 教学方法与手段

### 教学方法

1. **讲授法**：系统讲解函数的概念、语法和设计原则
2. **演示法**：通过实际代码演示展示函数的定义、调用和应用过程
3. **案例教学法**：通过实际项目案例展示函数的重要作用
4. **对比教学法**：对比不同参数传递方式的特点和适用场景
5. **重构教学法**：通过代码重构实践展示函数的价值和应用

### 教学手段

1. **多媒体课件**：使用 PPT 展示概念、语法和设计思路
2. **编程环境**：使用 Python IDE 进行实时代码演示和调试
3. **互动平台**：利用课堂互动系统进行实时问答和代码分享
4. **在线资源**：结合超星学习通平台进行课后练习和项目实践

## 教学过程设计

### 第一环节：课程导入（5 分钟）

#### 教学内容

**问题情境创设**：如何避免代码重复？如何让程序更加模块化？

#### 教学活动

1. **问题展示**（4 分钟）

   - 展示重复代码的问题：

   ```python
   # 重复的问候代码
   print("Hello, Alice!")
   print("Hello, Bob!")
   print("Hello, Charlie!")
   ```

   - 分析问题：代码冗余、维护困难、容易出错

2. **解决方案引入**（4 分钟）
   - 引出函数概念：可重复调用的代码块
   - 展示函数的优势：代码重用、模块化设计、易于维护

#### 设计意图

通过具体的代码问题激发学生学习函数的兴趣，建立函数必要性的认知。

### 第二环节：函数定义基础（8 分钟）

#### 教学内容一：函数的基本概念和定义（8 分钟）

**知识点**：函数的概念、作用和基本语法

**教学活动**：

1. **概念讲解**（2 分钟）

   - 函数的定义：一段可重复调用的代码块，用于执行特定的任务
   - 函数的作用：代码重用、模块化设计、提高可读性

2. **简单函数定义演示**（3 分钟）

   ```python
   def greet_user():
       """显示简单的问候语"""
       print("Hello!")

   greet_user()  # 函数调用
   ```

3. **形参与实参概念**（3 分钟）

   ```python
   def greet_user(username):  # username是形参（parameter）
       """显示简单的问候语"""
       print(f"Hello, {username.title()}!")

   greet_user('jesse')  # 'jesse'是实参（argument）
   ```

   - 形参（parameter）：username，函数用到的信息的名字
   - 实参（argument）：'jesse'，函数实际使用的信息

**教学方法**：讲授法 + 演示法
**设计意图**：建立函数的基本概念，掌握函数定义的基本语法

### 第三环节：传递实参（12 分钟）

#### 教学内容一：位置实参（5 分钟）

**知识点**：位置实参的概念和使用规则

**教学活动**：

1. **正确的位置实参**（2 分钟）

   ```python
   def describe_pet(animal_type, pet_name):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")

   describe_pet('cat', 'harry')
   # 运行结果：
   # I have a cat.
   # My cat's name is Harry.
   ```

2. **错误的位置实参顺序**（3 分钟）

   ```python
   # 如果我们混淆了位置实参的顺序，那就可能会得到一个怪物：
   describe_pet('harry', 'cat')
   # 运行结果：
   # I have a harry.
   # My harry's name is Cat.
   ```

**设计意图**：强调位置实参顺序的重要性

#### 教学内容二：关键字实参（3 分钟）

**知识点**：关键字实参的概念和优势

**教学活动**：

```python
# 关键字实参：函数调用时，明确指出实参对应的形参
def describe_pet(animal_type, pet_name):
    """显示宠物的信息"""
    print(f"\nI have a {animal_type}.")
    print(f"My {animal_type}'s name is {pet_name.title()}.")

describe_pet(animal_type='cat', pet_name='harry')
describe_pet(pet_name='harry', animal_type='cat')  # 顺序可变
```

**设计意图**：让学生理解关键字实参的灵活性

#### 教学内容三：默认值参数（4 分钟）

**知识点**：默认值参数的设计和使用

**教学活动**：

1. **基本默认值**（2 分钟）

   ```python
   def describe_pet(pet_name, animal_type='dog'):
       """显示宠物的信息"""
       print(f"\nI have a {animal_type}.")
       print(f"My {animal_type}'s name is {pet_name.title()}.")

   describe_pet('willie')  # 没有第二个参数，第二个参数用默认值
   # 运行结果：
   # I have a dog.
   # My dog's name is Willie.
   ```

2. **覆盖默认值**（2 分钟）

   ```python
   describe_pet('harry', 'cat')  # 有实参则默认值被忽略
   # 运行结果：
   # I have a cat.
   # My cat's name is Harry.
   ```

**设计意图**：让学生掌握默认值参数的灵活应用

#### 教学内容四：等效的函数调用和随堂练习（3 分钟）

**知识点**：多种调用方式的等效性

**教学活动**：

1. **等效调用方式讨论**（1 分钟）

   ```python
   def describe_pet(pet_name, animal_type='dog'):
       ...
   # 哪些函数调用的方式是等价的？
   describe_pet('willie')
   describe_pet(pet_name='willie')
   describe_pet('harry', 'cat')
   describe_pet(pet_name='harry', animal_type='cat')
   describe_pet(animal_type='cat', pet_name='harry')
   ```

2. **随堂练习 1：等效的函数调用**（2 分钟）

   ```python
   # 定义一个函数 introduce(name, hobby="编程")
   # 函数功能为打印出 "我是XXX，我喜欢YYY"
   # 要求：用三种方式调用该函数
   def introduce(name, hobby="编程"):
       print(f"我是{name}，我喜欢{hobby}")

   introduce("小明")  # （1）只传入一个参数
   introduce("小红", "打篮球")  # （2）位置传参两个参数
   introduce(hobby="读书", name="小刚")  # （3）关键字传参两个参数
   ```

**设计意图**：巩固参数传递的理解，培养实践能力

### 第四环节：返回值（10 分钟）

#### 教学内容一：返回简单的值（4 分钟）

**知识点**：函数返回值的基本概念和使用

**教学活动**：

```python
def get_formatted_name(first_name, last_name):
    """返回标准格式的姓名"""
    full_name = f"{first_name} {last_name}"
    return full_name.title()  # 返回关键字

musician = get_formatted_name('jimi', 'hendrix')  # 函数返回的值
print(musician)
# 运行结果：Jimi Hendrix
```

**设计意图**：让学生理解函数返回值的概念和作用

#### 教学内容二：如何让实参可选（3 分钟）

**知识点**：可选参数的设计方法

**教学活动**：

```python
def get_formatted_name(first_name, last_name, middle_name=''):
    """返回标准格式的姓名"""
    if middle_name:
        full_name = f"{first_name} {middle_name} {last_name}"
    else:
        full_name = f"{first_name} {last_name}"
    return full_name.title()

musician = get_formatted_name('jimi', 'hendrix')
print(musician)  # 运行结果：Jimi Hendrix
musician = get_formatted_name('john', 'hooker', 'lee')
print(musician)  # 运行结果：John Lee Hooker
```

**设计意图**：展示如何设计灵活的函数接口

#### 教学内容三：返回字典和随堂练习（3 分钟）

**知识点**：返回复杂数据结构

**教学活动**：

1. **随堂练习：返回字典**（2 分钟）

   ```python
   def build_person(first_name, last_name):
       """返回一个字典，其中包含有关一个人的信息"""
       # 在这里写一行代码，实现运行结果
       person = {'first': first_name, 'last': last_name}
       return person

   musician = build_person('jimi', 'hendrix')
   print(musician)
   # 运行结果：{'first': 'jimi', 'last': 'hendrix'}
   ```

2. **增加可选参数**（1 分钟）

   ```python
   def build_person(first_name, last_name, age=None):
       """返回一个字典，其中包含有关一个人的信息"""
       person = {'first': first_name, 'last': last_name}
       if age:
           person['age'] = age
       return person

   musician = build_person('jimi', 'hendrix', age=27)
   print(musician)
   # 运行结果：{'first': 'jimi', 'last': 'hendrix', 'age': 27}
   ```

**设计意图**：培养学生设计复杂返回值的能力

### 第五环节：传递列表和课堂练习（7 分钟）

#### 教学内容一：函数与列表的结合应用（4 分钟）

**知识点**：列表作为函数参数的处理

**教学活动**：

1. **基本列表传递**（1 分钟）

   ```python
   def greet_users(names):
       """向列表中的每个用户发出简单的问候"""
       for name in names:
           msg = f"Hello, {name.title()}!"
           print(msg)

   usernames = ['hannah', 'ty', 'margot']
   greet_users(usernames)
   # 运行结果：Hello, Hannah! Hello, Ty! Hello, Margot!
   ```

2. **在函数中修改列表（代码重构）**（3 分钟）

   ```python
   # 回忆7.3节中的代码：
   student_list = ['xiaoming', 'xiaohong', 'xiaoli']
   in_class = []
   while student_list:
       student = student_list.pop()
       print(f"{student.title()} 正在签到...")
       in_class.append(student)

   # 用函数重构：
   def sign_in_students(student_list, in_class):
       """将等待签到的学生逐一签到，添加到已签到列表中"""
       while student_list:
           current_student = student_list.pop()
           print(f"{current_student.title()} 正在签到...")
           in_class.append(current_student)

   def show_signed_in_students(in_class):
       """显示所有已完成签到的学生"""
       print("\n以下同学已完成签到：")
       for student in in_class:
           print(student.title())

   # 主程序
   student_list = ['xiaoming', 'xiaohong', 'xiaoli']
   in_class = []
   sign_in_students(student_list, in_class)
   show_signed_in_students(in_class)
   ```

**设计意图**：展示函数在代码重构中的重要作用

#### 教学内容二：讨论与练习（3 分钟）

**教学活动**：

1. **讨论：用函数重构代码有什么好处？**（1 分钟）

   - 代码分块
   - 每次只关注一小块，方便思考，方便纠错
   - 若要添加功能，只关注代码小块即可
   - 用函数调用同一块代码，而不是重复写代码

2. **函数随堂练习**（2 分钟）

   ```python
   # 示例输出：
   # Xiaotian 借出了图书...
   # Xiaozhi 借出了图书...
   # Xiaohua 借出了图书...
   # 以下同学已成功借书：
   # Xiaotian
   # Xiaozhi
   # Xiaohua

   waiting_list = ['xiaohua', 'xiaozhi', 'xiaotian']
   borrowed_list = []
   # ✍️ 请写出两个函数：
   # borrow_books(waiting_list, borrowed_list)
   # show_borrowed_books(borrowed_list)
   ```

**设计意图**：巩固学习成果，培养实践能力

### 第六环节：课堂小结（3 分钟）

#### 教学活动

1. **函数小结**（3 分钟）

   - 函数的定义：一段可重复调用的代码块，用于执行特定的任务
   - 形参和实参：形参是函数用到的信息的名字，实参是函数实际使用的信息
   - 参数的传递方式：位置实参、关键字实参、默认值参数
   - 返回值：函数中使用返回语句来返回的一个或一组值
   - 函数与循环的结合：提高代码的模块化和重用性

**设计意图**：巩固学习成果，构建完整知识体系

## 参考资料

1. 《Python 编程：从入门到实践》第 8 章
2. Python 官方文档：函数定义
3. 代码重构相关资料

## 作业布置

### 必做作业

1. **基础练习**：

   - 下周一前提交超星学习通第八章（上）作业
   - 设计一个学生成绩管理系统，包含多个函数模块

2. **应用练习**：
   - 完成课堂上的图书借阅系统练习
   - 要求：使用函数实现借书和显示功能，体现代码重构的优势
   - 功能：borrow_books()和 show_borrowed_books()两个函数

### 预习要求

1. **下节课预习**：

   - 预习第八章（下），思考哪些知识是重点，哪些知识是难点
   - 了解任意数量参数、模块导入等高级特性

2. **思考问题**：
   - 如何处理不确定数量的参数？
   - 如何将函数组织到模块中？

---

**教学反思**：
本节课重点培养学生的模块化编程思维。通过代码重构的实例让学生体会函数的价值。需要特别关注学生对参数传递方式的理解，适时进行练习巩固。
