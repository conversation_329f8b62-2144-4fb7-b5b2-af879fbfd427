# Python 程序设计课程教学设计方案（最新）

## 基本信息

- **课程名称**：Python 程序设计
- **授课章节**：第七章 用户输入和 while 循环
- **授课班级**：软件工程专业
- **授课时间**：45 分钟（1 学时）
- **授课教师**：冯子晋
- **授课地点**：计算机实验室

## 教学分析

### 学情分析

软件工程专业学生已掌握 Python 基础语法、列表、字典、for 循环等知识，具备较好的编程基础和逻辑思维能力。但对用户交互和不确定次数循环的理解还需要加强，特别是在软件开发中循环控制逻辑的设计能力有待提升。

### 教材分析

本章是 Python 程序设计中的重要内容，while 循环作为程序控制结构的重要组成部分，在软件开发、用户交互、数据处理等方面应用广泛。考虑到 45 分钟的时间限制，重点讲解 while 循环的核心概念和基本应用。

## 教学目标

### 知识目标

1. 掌握 input()函数的使用方法和数据类型转换
2. 理解 while 循环的工作原理和执行流程
3. 掌握 break、continue 等循环控制语句的使用
4. 了解自赋值运算符的概念和应用

### 能力目标

1. 能够设计和实现用户交互程序
2. 能够合理使用 while 循环解决实际问题
3. 能够避免死循环，设计安全的循环结构
4. 能够结合 while 循环处理列表和字典数据

### 素质目标

1. 培养学生的逻辑思维能力和程序设计思维
2. 提高学生的用户体验意识和交互设计能力
3. 培养学生良好的编程习惯和调试能力
4. 增强学生解决复杂问题的信心和能力

## 教学重点与难点

### 教学重点

1. input()函数的使用和数据类型转换技巧
2. while 循环的基本语法和执行机制
3. break 和 continue 语句的正确使用
4. while 循环与数据结构的结合应用

### 教学难点

1. 循环条件的设计和死循环的避免
2. 复杂交互逻辑的程序设计
3. 循环中的异常处理和边界条件
4. while 循环在实际项目中的合理应用

## 教学方法与手段

### 教学方法

1. **讲授法**：系统讲解 while 循环的概念、语法和执行机制
2. **演示法**：通过实际代码演示展示循环的执行过程和控制方法
3. **案例教学法**：通过实际应用案例帮助学生理解 while 循环的使用场景
4. **实践教学法**：通过编程练习让学生动手实践，掌握循环控制技巧
5. **对比教学法**：对比 for 循环和 while 循环的特点和适用场景

### 教学手段

1. **多媒体课件**：使用 PPT 展示概念、语法和执行流程
2. **编程环境**：使用 Python IDE 进行实时代码演示和调试
3. **互动平台**：利用课堂互动系统进行实时问答和反馈
4. **在线资源**：结合超星学习通平台进行课后练习和拓展

## 教学过程设计

### 第一环节：课程导入

#### 教学内容

**问题情境创设**：如何让程序与用户进行交互？如何处理不确定次数的重复操作？

#### 教学活动

1. **复习回顾**

   - 回顾 for 循环的特点：适用于确定次数的循环
   - 提问：如果不知道循环次数怎么办？

2. **问题引入**
   - 展示实际场景：ATM 取款、用户登录验证、菜单选择
   - 分析问题：这些场景的共同特点是什么？
   - 引出 while 循环：适用于不确定次数的循环控制

### 第二环节：基础知识学习

#### 教学内容一：input()函数的工作原理

**知识点**：用户输入处理和数据类型转换

**教学活动**：

1. **基本用法演示**

   ```python
   name = input("请输入你的名字：")
   print(f"\n{name}来上课了！")
   ```

2. **自赋值运算符讲解**

   ```python
   # 常见的分行追加
   msg = "我们需要你的名字来填写课程反馈问卷"
   msg = msg + "\n请输入你的名字："

   # 使用自赋值运算符简化
   msg = "我是小明"
   msg += "\n我来上课了"
   print(msg)

   # 常用的自赋值运算符
   number += 1  # number = number + 1
   number -= 1  # number = number - 1
   number *= 1  # number = number * 1
   number /= 1  # number = number / 1
   ```

3. **数据类型转换**

   ```python
   # 错误示例：把文本当作数值直接使用
   age = input("How old are you? ")
   print(age >= 18)  # TypeError

   # 正确示例：使用int()函数转换
   age = input("How old are you? ")
   age = int(age)  # 把字符串变成数值
   print(age >= 18)  # 正常运行
   ```

#### 教学内容二：求模运算符应用

**知识点**：求模运算符的概念和实际应用

**教学活动**：

1. **基本概念**

   ```python
   print(4 % 3)  # 输出：1
   print(5 % 3)  # 输出：2
   print(6 % 3)  # 输出：0
   print(7 % 3)  # 输出：1
   ```

2. **实际应用：判断奇偶数**
   ```python
   print(4 % 2 == 0)  # True（偶数）
   print(5 % 2 == 0)  # False（奇数）
   print(7 % 2 == 0)  # False（奇数）
   print(0 % 2 == 0)  # True（偶数）
   ```

#### 教学内容三：引入 while 循环

**知识点**：for 循环的局限性和 while 循环的必要性

**教学活动**：

1. **for 循环的局限性**

   ```python
   # 用for循环记录校园跑的问题
   total = 120  # 小明的目标是120公里
   for day in range(100):
       # 输入每一天小明跑的公里数
       # total -= 每一天小明跑的公里数
       if total <= 0:
           print(f"小明在第{day + 1}天达到了目标")
           break
       else:
           print(f"小明还需要再跑{total}公里才能达到目标")
   ```

2. **while 循环的基本语法**
   ```python
   # for循环用于循环的最大次数是限定的
   # while循环则是一直执行，直到条件不满足为止
   current_number = 1
   while current_number < 3:
       print(current_number)
       current_number += 1  # 代码current_number += 1是current_number = current_number + 1的简写
   ```

#### 教学内容四：while 循环的退出方式

**知识点**：while 循环的三种主要退出方式

**教学活动**：

1. **使用条件表达式退出**

   ```python
   prompt = "你想吃什么外卖？输入 '不点了' 来结束\n"
   food = ""
   while food != '不点了':
       food = input(prompt)
       print(f"好的，已添加：{food}")
   ```

2. **使用布尔标志退出**
   ```python
   prompt = "你想吃什么外卖？输入 '不点了' 来结束\n"
   ordering = True  # 布尔标志变量
   food = ""
   while ordering:
       food = input(prompt)
       if food == '不点了':
           ordering = False
       else:
           print(f"好的，已添加：{food}")
   ```

### 第三环节：while 循环核心应用

#### 教学内容一：使用 break 退出循环

**知识点**：break 关键字的使用

**教学活动**：

1. **break 基本用法**

   ```python
   # break关键字：立即退出循环
   current_number = 0
   while True:
       if current_number > 5:
           break
       current_number += 1
       print(current_number)
   ```

2. **运行结果分析**
   - 输出：1, 2, 3, 4, 5, 6
   - 解释 break 的执行时机和效果

#### 教学内容二：使用 continue 跳过循环

**知识点**：continue 关键字的使用

**教学活动**：

1. **continue 基本用法**

   ```python
   # continue（继续）关键字：终止本轮的循环，继续下一轮循环
   current_number = 0
   while True:
       current_number += 1
       if current_number % 2 == 0:
           continue  # 当current_number是偶数，跳过打印
       print(current_number)
   ```

2. **课堂练习：同时使用 continue 与 break**
   ```python
   # 思考运行结果是什么？为什么？
   current_number = 0
   while True:
       if current_number > 5:
           break
       current_number += 1
       if current_number % 2 == 0:
           continue
       print(current_number)
   # 运行结果：1, 3, 5
   ```

#### 教学内容三：避免死循环

**知识点**：死循环的识别和预防

**教学活动**：

```python
# 死循环示例
x = 1
while x <= 5:
    print(x)  # 忘记更新x，造成死循环
# 按Ctrl+C来结束程序的运行
# 更重要是用条件测试表达式、标志（变量）、break关键字
```

#### 教学内容四：课堂讨论：区分 for 循环和 while 循环

**知识点**：for 循环和 while 循环的选择原则

**教学活动**：

1. **讨论题目**
   以下情景中应该使用 for 循环还是 while 循环：

   - 感冒了，你提醒自己一天吃药三次
   - 你和朋友发消息，直到对方回"睡了"
   - 你考英语四级，直到考过为止

2. **答案分析**

   ```python
   # 1. 感冒了，一天吃药三次 - for循环
   for i in range(3):
       print(f"第 {i+1} 次吃药")

   # 2. 朋友发消息，直到对方回"睡了" - while循环
   msg = ""
   while msg != "睡了":
       msg = input("对方回复：")

   # 3. 考英语四级，直到考过为止 - while循环
   attempts = 0
   while attempts < 8:
       attempts += 1
       passed = input(f"第 {attempts} 次考试，四级过了吗？(yes/no): ")
       if passed == "yes":
           print("恭喜，四级通过！")
           break
   else:
       print("8 次机会已用完，四级还是没过。")
   ```

### 第四环节：while 循环与列表和字典

#### 教学内容：while 循环处理数据结构

**知识点**：while 循环与列表、字典的结合应用

**教学活动**：

1. **while 循环与列表：案例一**

   ```python
   # 学生签到系统
   student_list = ['xiaoming', 'xiaohong', 'xiaoli']
   in_class = []

   while student_list:
       student = student_list.pop()
       print(f"{student.title()} 正在签到...")
       in_class.append(student)

   print("\n以下同学已完成签到：")
   for student in in_class:
       print(student.title())
   ```

2. **while 循环与列表：案例二**

   ```python
   # 删除列表中的全部匹配项
   pets = ['dog', 'cat', 'dog', 'cat', 'rabbit']
   print(pets)

   while 'cat' in pets:
       pets.remove('cat')  # remove()方法仅能移除首个匹配，用while删除全部匹配

   print(pets)  # 输出：['dog', 'dog', 'rabbit']
   ```

3. **while 循环与字典：案例三**

   ```python
   # 记录每个人毕业旅行想去哪里
   votes = {}

   while True:
       name = input("\n请输入你的名字：")
       place = input("你最想毕业旅行去哪里？")
       votes[name] = place

       cont = input("还有人要投票吗？(yes/no)：")
       if cont == 'no':
           break

   print("\n投票结果如下：")
   for name, place in votes.items():
       print(f"{name} 想去 {place}")
   ```

### 第五环节：课堂小结

#### 教学活动

1. **知识小结**

   - input()函数：显示提示，等待用户输入
   - while 循环：在条件不满足时停止执行反复的操作
   - 控制 while 循环流程的方式：条件表达式、标志、break、continue
   - 结合 while 循环来操作列表和字典

2. **while 循环小结**
   - 使用条件表达式退出：通过设定条件触发循环停止
   - 使用标志退出（布尔变量记录条件是否满足）：利用标志变量调整控制流程
   - 使用 break 退出：立即终止循环
   - 使用 continue 跳过循环：跳过当前循环中的剩余代码，进入下次循环

## 参考资料

1. 《Python 编程：从入门到实践》第 7 章
2. Python 官方文档：控制流程语句
3. 在线编程练习平台相关题目

## 作业布置

### 必做作业

1. **基础练习**：

   - 下周一前提交超星学习通第七章作业
   - 编写一个简单的菜单驱动程序，使用 while 循环实现用户交互

2. **应用练习**：
   - 设计一个简单的学生信息管理系统
   - 要求：使用 while 循环处理用户输入，结合字典存储学生信息
   - 功能：添加学生、查询学生、删除学生、显示所有学生

### 预习要求

1. **下节课预习**：

   - 预习第八章内容，思考哪些知识是重点，哪些知识是难点
   - 思考如何将重复的代码块封装成函数

2. **思考问题**：
   - 函数在程序设计中的作用是什么？
   - 如何设计合理的函数参数和返回值？

---

**教学反思**：
本节课重点在于让学生理解 while 循环的控制逻辑，特别要注意死循环的预防。通过实际案例让学生体会 while 循环在处理用户交互和数据处理中的重要作用。
